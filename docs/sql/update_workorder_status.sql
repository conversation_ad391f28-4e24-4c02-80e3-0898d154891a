-- 更新工单状态字典数据
-- 删除旧的工单状态字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'sdl_ticket_status';

-- 插入新的工单状态字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '未开始', '0', 'sdl_ticket_status', '', 'info', 'Y', '0', 'admin', now(), '工单新建时的默认状态'),
(2, '进行中', '1', 'sdl_ticket_status', '', 'primary', 'N', '0', 'admin', now(), '工单被指派给测试人员后的状态'),
(3, '已关闭', '2', 'sdl_ticket_status', '', 'success', 'N', '0', 'admin', now(), '测试完成后的状态'),
(4, '被阻塞', '3', 'sdl_ticket_status', '', 'warning', 'N', '0', 'admin', now(), '工单因某些原因无法继续进行'),
(5, '已延期', '4', 'sdl_ticket_status', '', 'danger', 'N', '0', 'admin', now(), '超过期望完成时间的工单');

-- 更新现有工单的状态值（如果有的话）
UPDATE sdl_test_workorder SET ticket_status = '0' WHERE ticket_status = 'NOT_STARTED' OR ticket_status = '未开始' OR ticket_status IS NULL OR ticket_status = '';
UPDATE sdl_test_workorder SET ticket_status = '1' WHERE ticket_status = 'IN_PROGRESS' OR ticket_status = '进行中';
UPDATE sdl_test_workorder SET ticket_status = '2' WHERE ticket_status = 'CLOSED' OR ticket_status = '已关闭';
UPDATE sdl_test_workorder SET ticket_status = '3' WHERE ticket_status = 'BLOCKED' OR ticket_status = '被阻塞';
UPDATE sdl_test_workorder SET ticket_status = '4' WHERE ticket_status = 'OVERDUE' OR ticket_status = '已延期';

-- 系统配置：默认指派人配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES
('工单默认指派人', 'workorder.default.assignee', 'admin', 'Y', 'admin', now(), '工单新建时的默认指派人用户名，可在系统参数中修改')
ON DUPLICATE KEY UPDATE config_value = 'admin';
