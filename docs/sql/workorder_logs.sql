-- ----------------------------
-- 工单日志表
-- ----------------------------
DROP TABLE IF EXISTS `sdl_workorder_logs`;
CREATE TABLE `sdl_workorder_logs` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `ticket_id` bigint(20) NOT NULL COMMENT '工单ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型(CREATE:创建,ASSIGN:指派,COMPLETE:完成,BLOCK:阻塞,OVERDUE:延期,STATUS_CHANGE:状态变更)',
  `before_status` varchar(50) DEFAULT NULL COMMENT '操作前状态',
  `after_status` varchar(50) DEFAULT NULL COMMENT '操作后状态',
  `before_handler` varchar(100) DEFAULT NULL COMMENT '操作前处理人',
  `after_handler` varchar(100) DEFAULT NULL COMMENT '操作后处理人',
  `before_handler_id` bigint(20) DEFAULT NULL COMMENT '操作前处理人ID',
  `after_handler_id` bigint(20) DEFAULT NULL COMMENT '操作后处理人ID',
  `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_ticket_id` (`ticket_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工单日志表';

-- ----------------------------
-- 更新工单状态字典数据
-- ----------------------------
-- 删除旧的工单状态字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'sdl_ticket_status';

-- 插入新的工单状态字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '未开始', 'NOT_STARTED', 'sdl_ticket_status', '', 'info', 'Y', '0', 'admin', sysdate(), '工单新建时的默认状态'),
(2, '进行中', 'IN_PROGRESS', 'sdl_ticket_status', '', 'primary', 'N', '0', 'admin', sysdate(), '工单被指派给测试人员后的状态'),
(3, '已关闭', 'CLOSED', 'sdl_ticket_status', '', 'success', 'N', '0', 'admin', sysdate(), '测试完成后的状态'),
(4, '被阻塞', 'BLOCKED', 'sdl_ticket_status', '', 'warning', 'N', '0', 'admin', sysdate(), '工单因某些原因无法继续进行'),
(5, '已延期', 'OVERDUE', 'sdl_ticket_status', '', 'danger', 'N', '0', 'admin', sysdate(), '超过期望完成时间的工单');

-- ----------------------------
-- 更新测试工单表结构，添加处理人ID字段
-- ----------------------------
ALTER TABLE `sdl_test_workorder` ADD COLUMN `handler_id` bigint(20) DEFAULT NULL COMMENT '当前处理人ID' AFTER `handler`;

-- 创建索引
ALTER TABLE `sdl_test_workorder` ADD INDEX `idx_handler_id` (`handler_id`);
ALTER TABLE `sdl_test_workorder` ADD INDEX `idx_ticket_status` (`ticket_status`);
ALTER TABLE `sdl_test_workorder` ADD INDEX `idx_expect_finish_time` (`expect_finish_time`);

-- ----------------------------
-- 系统配置：默认指派人配置
-- ----------------------------
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES
('工单默认指派人', 'workorder.default.assignee', 'admin', 'Y', 'admin', sysdate(), '工单新建时的默认指派人用户名，可在系统参数中修改');

-- ----------------------------
-- 定时任务配置：工单延期检查
-- ----------------------------
INSERT INTO sys_job (job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) VALUES
('工单延期检查', 'DEFAULT', 'workOrderTask.checkOverdueWorkOrders', '0 0 0 * * ?', '3', '1', '1', 'admin', sysdate(), '每日0点检查工单是否延期，自动标记延期工单');

-- ----------------------------
-- 权限配置：工单生命周期管理权限
-- ----------------------------
-- 指派工单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('工单指派', (SELECT menu_id FROM sys_menu WHERE menu_name = '测试工单' AND menu_type = 'C'), 1, '', '', 1, 0, 'F', '0', '0', 'business:workorder:assign', '#', 'admin', sysdate(), '');

-- 完成工单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('工单完成', (SELECT menu_id FROM sys_menu WHERE menu_name = '测试工单' AND menu_type = 'C'), 2, '', '', 1, 0, 'F', '0', '0', 'business:workorder:complete', '#', 'admin', sysdate(), '');

-- 阻塞工单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('工单阻塞', (SELECT menu_id FROM sys_menu WHERE menu_name = '测试工单' AND menu_type = 'C'), 3, '', '', 1, 0, 'F', '0', '0', 'business:workorder:block', '#', 'admin', sysdate(), '');
