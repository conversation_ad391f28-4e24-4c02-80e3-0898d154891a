# 测试工单生命周期管理系统部署指南

## 系统概述

本系统实现了测试工单的完整生命周期管理，包括5种状态管理：未开始、进行中、已关闭、被阻塞、已延期。

### 主要功能

1. **工单状态管理**：支持5种状态的流转控制
2. **工单指派**：支持将工单指派给指定用户
3. **工单完成**：支持标记工单为已完成状态
4. **工单阻塞**：支持阻塞工单并记录原因
5. **延期检查**：定时任务自动检查并标记延期工单
6. **操作日志**：完整记录工单生命周期的所有操作
7. **用户选择**：支持搜索和选择系统用户

## 部署步骤

### 1. 数据库配置

执行以下SQL脚本创建相关表和配置：

```bash
mysql -u root -p your_database < docs/sql/workorder_logs.sql
```

该脚本包含：
- 创建工单日志表 `sdl_workorder_logs`
- 更新工单状态字典数据
- 添加工单表的处理人ID字段
- 创建系统配置和定时任务
- 添加权限配置

### 2. 后端部署

#### 2.1 编译项目

```bash
cd sdl-platform
mvn clean compile
```

#### 2.2 运行测试

```bash
./bin/run-tests.sh
```

#### 2.3 启动应用

```bash
mvn spring-boot:run -pl sdl-platform-admin
```

### 3. 前端部署

#### 3.1 安装依赖

```bash
cd sdl-platform-vue3
npm install
```

#### 3.2 启动开发服务器

```bash
npm run dev
```

#### 3.3 构建生产版本

```bash
npm run build
```

### 4. 定时任务配置

系统会自动创建延期检查定时任务，默认每日0点执行。可在系统管理 -> 定时任务中查看和管理。

## 使用指南

### 1. 工单创建

- 新建工单时，系统自动设置状态为"未开始"
- 自动指派给配置的默认处理人（默认为admin）
- 记录创建日志

### 2. 工单指派

- 在工单详情页面点击"指派"按钮
- 选择目标处理人（支持搜索）
- 工单状态自动变更为"进行中"
- 记录指派日志

### 3. 工单完成

- 在工单详情页面点击"完成"按钮
- 工单状态变更为"已关闭"
- 自动设置实际完成时间
- 记录完成日志

### 4. 工单阻塞

- 在工单详情页面点击"阻塞"按钮
- 输入阻塞原因
- 工单状态变更为"被阻塞"
- 记录阻塞日志

### 5. 延期检查

- 系统每日0点自动检查
- 对比期望完成时间与当前日期
- 自动标记延期工单
- 记录延期日志

### 6. 工单日志

- 在工单详情页面查看完整操作日志
- 显示时间线格式的操作历史
- 包含操作类型、状态变更、处理人变更等信息

## 状态流转规则

```
未开始 → 进行中 → 已关闭
   ↓        ↓        ↑
被阻塞 ←→ 被阻塞 ←→ 被阻塞
   ↓        ↓
已延期 → 已关闭
```

### 详细规则

1. **未开始** → **进行中**：通过指派操作
2. **进行中** → **已关闭**：通过完成操作
3. **进行中** → **已延期**：系统自动检查
4. **已延期** → **已关闭**：通过完成操作
5. **任何状态** → **被阻塞**：通过阻塞操作
6. **被阻塞** → **其他状态**：根据原状态恢复

## API接口

### 工单管理

- `GET /business/workorder/list` - 查询工单列表
- `GET /business/workorder/{id}` - 获取工单详情
- `POST /business/workorder` - 创建工单
- `PUT /business/workorder` - 更新工单
- `DELETE /business/workorder/{ids}` - 删除工单

### 生命周期管理

- `POST /business/workorder/assign` - 指派工单
- `POST /business/workorder/complete/{id}` - 完成工单
- `POST /business/workorder/block` - 阻塞工单
- `GET /business/workorder/logs/{id}` - 获取工单日志
- `GET /business/workorder/overdue` - 查询延期工单

## 权限配置

系统新增以下权限：

- `business:workorder:assign` - 工单指派权限
- `business:workorder:complete` - 工单完成权限
- `business:workorder:block` - 工单阻塞权限

## 系统配置

- `workorder.default.assignee` - 默认指派人配置

## 故障排除

### 1. 定时任务不执行

检查定时任务状态：
```sql
SELECT * FROM sys_job WHERE job_name = '工单延期检查';
```

### 2. 状态流转失败

检查工单当前状态和目标状态是否符合流转规则。

### 3. 用户选择不显示

确认用户管理模块API正常，检查用户权限。

### 4. 日志记录失败

检查工单日志表是否创建成功，确认数据库连接正常。

## 技术栈

- **后端**：Spring Boot, MyBatis, MySQL
- **前端**：Vue 3, Element Plus
- **定时任务**：Quartz
- **测试**：JUnit 5, Mockito

## 联系方式

如有问题，请联系开发团队。
