<template>
  <div class="app-container">
    <el-page-header @back="goBack" :content="pageTitle" />

    <!-- 操作按钮 -->
    <div style="margin-top: 20px; text-align: right;">
      <el-button
        v-if="canAssign"
        type="primary"
        icon="User"
        @click="showAssignDialog = true"
        v-hasPermi="['business:workorder:assign']">
        指派
      </el-button>
      <el-button
        v-if="canComplete"
        type="success"
        icon="Check"
        @click="handleComplete"
        v-hasPermi="['business:workorder:complete']">
        完成
      </el-button>
      <el-button
        v-if="canBlock"
        type="warning"
        icon="Lock"
        @click="showBlockDialog = true"
        v-hasPermi="['business:workorder:block']">
        阻塞
      </el-button>
    </div>

    <el-card class="box-card" style="margin-top: 20px;">
      <!-- 工单详细信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工单ID">
          {{ workorderInfo.ticketId || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="工单标题">
          {{ workorderInfo.ticketTitle || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="测试类型">
          <dict-tag :options="sdl_test_type" :value="workorderInfo.ticketType"/>
        </el-descriptions-item>
        <el-descriptions-item label="工单状态">
          <dict-tag :options="sdl_ticket_status" :value="workorderInfo.ticketStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="所属项目">
          {{ workorderInfo.projectName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="系统名称">
          {{ workorderInfo.systemName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="系统类型">
          <dict-tag :options="sdl_system_type" :value="workorderInfo.systemType"/>
        </el-descriptions-item>
        <el-descriptions-item label="项目经理">
          {{ workorderInfo.projectManager || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="软件工程师">
          {{ workorderInfo.softwareEngineer || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="测试申请人">
          {{ workorderInfo.testApplicant || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="安全对接人">
          {{ workorderInfo.securityRepresentative || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="当前处理人">
          {{ workorderInfo.handler || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <dict-tag :options="sdl_priority" :value="workorderInfo.priority"/>
        </el-descriptions-item>
        <el-descriptions-item label="期望完成时间">
          {{ parseTime(workorderInfo.expectFinishTime, '{y}-{m}-{d}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="实际完成时间">
          {{ parseTime(workorderInfo.actualFinishTime, '{y}-{m}-{d}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(workorderInfo.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(workorderInfo.updateTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="测试环境地址" :span="2">
          <div style="word-break: break-all;">
            {{ workorderInfo.testEnv || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="测试账号密码" :span="2">
          <div style="word-break: break-all;">
            {{ workorderInfo.testAccount || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="代码仓库地址" :span="2">
          <div style="word-break: break-all;">
            <el-link v-if="workorderInfo.codeRepo" :href="workorderInfo.codeRepo" target="_blank" type="primary">
              {{ workorderInfo.codeRepo }}
            </el-link>
            <span v-else>-</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="测试范围" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.trestScope || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="附件信息" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.attachment || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.remark || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 工单日志 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>工单日志</span>
          <el-button type="text" @click="getWorkorderLogs">刷新</el-button>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="log in workorderLogs"
          :key="log.logId"
          :timestamp="parseTime(log.operationTime)"
          placement="top">
          <el-card>
            <h4>{{ getOperationTypeName(log.operationType) }}</h4>
            <p>{{ log.operationDesc }}</p>
            <p v-if="log.beforeStatus && log.afterStatus">
              状态变更：{{ getStatusName(log.beforeStatus) }} → {{ getStatusName(log.afterStatus) }}
            </p>
            <p v-if="log.beforeHandler && log.afterHandler && log.beforeHandler !== log.afterHandler">
              处理人变更：{{ log.beforeHandler }} → {{ log.afterHandler }}
            </p>
            <p style="color: #909399; font-size: 12px;">操作人：{{ log.operatorName }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 指派对话框 -->
    <el-dialog title="指派工单" v-model="showAssignDialog" width="500px" append-to-body>
      <el-form ref="assignFormRef" :model="assignForm" :rules="assignRules" label-width="100px">
        <el-form-item label="指派给" prop="handlerId">
          <el-select
            v-model="assignForm.handlerId"
            placeholder="请选择处理人"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userLoading"
            style="width: 100%"
            @change="handleUserChange">
            <el-option
              v-for="user in userOptions"
              :key="user.userId"
              :label="user.nickName + '(' + user.userName + ')'"
              :value="user.userId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAssignDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAssign" :loading="assignLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 阻塞对话框 -->
    <el-dialog title="阻塞工单" v-model="showBlockDialog" width="500px" append-to-body>
      <el-form ref="blockFormRef" :model="blockForm" :rules="blockRules" label-width="100px">
        <el-form-item label="阻塞原因" prop="reason">
          <el-input
            v-model="blockForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入阻塞原因"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBlockDialog = false">取消</el-button>
          <el-button type="primary" @click="handleBlock" :loading="blockLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkorderDetail">
import { getWorkorder, assignWorkorder, completeWorkorder, blockWorkorder, getWorkorderLogs } from "@/api/business/workorder.js"
import { listUser } from "@/api/system/user.js"
import { ref, getCurrentInstance, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const { sdl_ticket_status, sdl_priority, sdl_test_type, sdl_system_type } = proxy.useDict('sdl_ticket_status', 'sdl_priority', 'sdl_test_type', 'sdl_system_type')

const workorderInfo = ref({})
const pageTitle = ref('工单详情')
const workorderLogs = ref([])

// 对话框显示状态
const showAssignDialog = ref(false)
const showBlockDialog = ref(false)

// 指派表单
const assignForm = ref({
  handlerId: null,
  handlerName: ''
})
const assignRules = {
  handlerId: [{ required: true, message: '请选择处理人', trigger: 'change' }]
}
const assignLoading = ref(false)

// 阻塞表单
const blockForm = ref({
  reason: ''
})
const blockRules = {
  reason: [{ required: true, message: '请输入阻塞原因', trigger: 'blur' }]
}
const blockLoading = ref(false)

// 用户选择
const userOptions = ref([])
const userLoading = ref(false)

// 计算属性：判断按钮是否可用
const canAssign = computed(() => {
  return workorderInfo.value.ticketStatus === 'NOT_STARTED' ||
         workorderInfo.value.ticketStatus === 'BLOCKED'
})

const canComplete = computed(() => {
  return workorderInfo.value.ticketStatus === 'IN_PROGRESS' ||
         workorderInfo.value.ticketStatus === 'OVERDUE'
})

const canBlock = computed(() => {
  return workorderInfo.value.ticketStatus !== 'CLOSED' &&
         workorderInfo.value.ticketStatus !== 'BLOCKED'
})

/** 返回上一页 */
function goBack() {
  router.back()
}

/** 获取工单详情 */
function getWorkorderDetail() {
  const ticketId = route.query.id
  if (ticketId) {
    getWorkorder(ticketId).then(response => {
      workorderInfo.value = response.data
      pageTitle.value = `${workorderInfo.value.ticketTitle}`
      // 获取工单日志
      getWorkorderLogsData()
    }).catch(() => {
      proxy.$modal.msgError('获取工单详情失败')
      router.back()
    })
  } else {
    proxy.$modal.msgError('缺少工单ID参数')
    router.back()
  }
}

/** 获取工单日志 */
function getWorkorderLogsData() {
  const ticketId = route.query.id
  if (ticketId) {
    getWorkorderLogs(ticketId).then(response => {
      workorderLogs.value = response.data
    }).catch(() => {
      console.error('获取工单日志失败')
    })
  }
}

/** 搜索用户 */
function searchUsers(query) {
  if (query !== '') {
    userLoading.value = true
    listUser({ userName: query, pageSize: 20 }).then(response => {
      userOptions.value = response.rows
      userLoading.value = false
    }).catch(() => {
      userLoading.value = false
    })
  } else {
    userOptions.value = []
  }
}

/** 用户选择变更 */
function handleUserChange(userId) {
  const user = userOptions.value.find(u => u.userId === userId)
  if (user) {
    assignForm.value.handlerName = user.nickName || user.userName
  }
}

/** 指派工单 */
function handleAssign() {
  proxy.$refs.assignFormRef.validate(valid => {
    if (valid) {
      assignLoading.value = true
      const data = {
        ticketId: workorderInfo.value.ticketId,
        handlerId: assignForm.value.handlerId,
        handlerName: assignForm.value.handlerName
      }
      assignWorkorder(data).then(() => {
        ElMessage.success('指派成功')
        showAssignDialog.value = false
        assignLoading.value = false
        // 重新获取工单详情
        getWorkorderDetail()
      }).catch(() => {
        assignLoading.value = false
      })
    }
  })
}

/** 完成工单 */
function handleComplete() {
  ElMessageBox.confirm('确认完成此工单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    completeWorkorder(workorderInfo.value.ticketId).then(() => {
      ElMessage.success('工单已完成')
      // 重新获取工单详情
      getWorkorderDetail()
    })
  })
}

/** 阻塞工单 */
function handleBlock() {
  proxy.$refs.blockFormRef.validate(valid => {
    if (valid) {
      blockLoading.value = true
      const data = {
        ticketId: workorderInfo.value.ticketId,
        reason: blockForm.value.reason
      }
      blockWorkorder(data).then(() => {
        ElMessage.success('工单已阻塞')
        showBlockDialog.value = false
        blockLoading.value = false
        // 重新获取工单详情
        getWorkorderDetail()
      }).catch(() => {
        blockLoading.value = false
      })
    }
  })
}

/** 获取操作类型名称 */
function getOperationTypeName(type) {
  const typeMap = {
    'CREATE': '创建工单',
    'ASSIGN': '指派工单',
    'REASSIGN': '重新指派',
    'COMPLETE': '完成工单',
    'BLOCK': '阻塞工单',
    'OVERDUE': '延期工单',
    'STATUS_CHANGE': '状态变更'
  }
  return typeMap[type] || type
}

/** 获取状态名称 */
function getStatusName(status) {
  const statusOption = sdl_ticket_status.value.find(item => item.value === status)
  return statusOption ? statusOption.label : status
}

onMounted(() => {
  getWorkorderDetail()
})
</script>

<style scoped>
.box-card {
  min-height: 500px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>