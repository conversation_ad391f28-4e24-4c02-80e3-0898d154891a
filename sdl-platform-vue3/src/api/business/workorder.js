import request from '@/utils/request'

// 查询测试工单列表
export function listWorkorder(query) {
  return request({
    url: '/business/workorder/list',
    method: 'get',
    params: query
  })
}

// 查询测试工单详细
export function getWorkorder(ticketId) {
  return request({
    url: '/business/workorder/' + ticketId,
    method: 'get'
  })
}

// 新增测试工单
export function addWorkorder(data) {
  return request({
    url: '/business/workorder',
    method: 'post',
    data: data
  })
}

// 修改测试工单
export function updateWorkorder(data) {
  return request({
    url: '/business/workorder',
    method: 'put',
    data: data
  })
}

// 删除测试工单
export function delWorkorder(ticketId) {
  return request({
    url: '/business/workorder/' + ticketId,
    method: 'delete'
  })
}

// 指派工单
export function assignWorkorder(data) {
  return request({
    url: '/business/workorder/assign',
    method: 'post',
    data: data
  })
}

// 完成工单
export function completeWorkorder(ticketId) {
  return request({
    url: '/business/workorder/complete/' + ticketId,
    method: 'post'
  })
}

// 阻塞工单
export function blockWorkorder(data) {
  return request({
    url: '/business/workorder/block',
    method: 'post',
    data: data
  })
}

// 获取工单日志
export function getWorkorderLogs(ticketId) {
  return request({
    url: '/business/workorder/logs/' + ticketId,
    method: 'get'
  })
}

// 查询延期工单
export function listOverdueWorkorder() {
  return request({
    url: '/business/workorder/overdue',
    method: 'get'
  })
}
