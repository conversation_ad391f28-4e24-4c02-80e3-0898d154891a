package com.leapmotor.sdl.business.enums;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

/**
 * 工单状态枚举测试
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
class WorkOrderStatusEnumTest {

    @Test
    void testGetByCode() {
        // Test valid codes
        assertEquals(WorkOrderStatusEnum.NOT_STARTED, WorkOrderStatusEnum.getByCode("NOT_STARTED"));
        assertEquals(WorkOrderStatusEnum.IN_PROGRESS, WorkOrderStatusEnum.getByCode("IN_PROGRESS"));
        assertEquals(WorkOrderStatusEnum.CLOSED, WorkOrderStatusEnum.getByCode("CLOSED"));
        assertEquals(WorkOrderStatusEnum.BLOCKED, WorkOrderStatusEnum.getByCode("BLOCKED"));
        assertEquals(WorkOrderStatusEnum.OVERDUE, WorkOrderStatusEnum.getByCode("OVERDUE"));
        
        // Test invalid code
        assertNull(WorkOrderStatusEnum.getByCode("INVALID_STATUS"));
        assertNull(WorkOrderStatusEnum.getByCode(null));
    }

    @Test
    void testCanTransition() {
        // Test normal transitions
        assertTrue(WorkOrderStatusEnum.canTransition("NOT_STARTED", "IN_PROGRESS"));
        assertTrue(WorkOrderStatusEnum.canTransition("IN_PROGRESS", "CLOSED"));
        assertTrue(WorkOrderStatusEnum.canTransition("IN_PROGRESS", "OVERDUE"));
        assertTrue(WorkOrderStatusEnum.canTransition("OVERDUE", "CLOSED"));
        
        // Test transitions to BLOCKED (allowed from any status)
        assertTrue(WorkOrderStatusEnum.canTransition("NOT_STARTED", "BLOCKED"));
        assertTrue(WorkOrderStatusEnum.canTransition("IN_PROGRESS", "BLOCKED"));
        assertTrue(WorkOrderStatusEnum.canTransition("CLOSED", "BLOCKED"));
        assertTrue(WorkOrderStatusEnum.canTransition("OVERDUE", "BLOCKED"));
        
        // Test transitions from BLOCKED (allowed to any status except OVERDUE)
        assertTrue(WorkOrderStatusEnum.canTransition("BLOCKED", "NOT_STARTED"));
        assertTrue(WorkOrderStatusEnum.canTransition("BLOCKED", "IN_PROGRESS"));
        assertTrue(WorkOrderStatusEnum.canTransition("BLOCKED", "CLOSED"));
        assertFalse(WorkOrderStatusEnum.canTransition("BLOCKED", "OVERDUE"));
        
        // Test invalid transitions
        assertFalse(WorkOrderStatusEnum.canTransition("NOT_STARTED", "CLOSED"));
        assertFalse(WorkOrderStatusEnum.canTransition("NOT_STARTED", "OVERDUE"));
        assertFalse(WorkOrderStatusEnum.canTransition("CLOSED", "IN_PROGRESS"));
        assertFalse(WorkOrderStatusEnum.canTransition("CLOSED", "NOT_STARTED"));
        assertFalse(WorkOrderStatusEnum.canTransition("CLOSED", "OVERDUE"));
        
        // Test with invalid status codes
        assertFalse(WorkOrderStatusEnum.canTransition("INVALID_STATUS", "IN_PROGRESS"));
        assertFalse(WorkOrderStatusEnum.canTransition("NOT_STARTED", "INVALID_STATUS"));
        assertFalse(WorkOrderStatusEnum.canTransition(null, "IN_PROGRESS"));
        assertFalse(WorkOrderStatusEnum.canTransition("NOT_STARTED", null));
    }

    @Test
    void testEnumProperties() {
        // Test NOT_STARTED
        assertEquals("NOT_STARTED", WorkOrderStatusEnum.NOT_STARTED.getCode());
        assertEquals("未开始", WorkOrderStatusEnum.NOT_STARTED.getInfo());
        
        // Test IN_PROGRESS
        assertEquals("IN_PROGRESS", WorkOrderStatusEnum.IN_PROGRESS.getCode());
        assertEquals("进行中", WorkOrderStatusEnum.IN_PROGRESS.getInfo());
        
        // Test CLOSED
        assertEquals("CLOSED", WorkOrderStatusEnum.CLOSED.getCode());
        assertEquals("已关闭", WorkOrderStatusEnum.CLOSED.getInfo());
        
        // Test BLOCKED
        assertEquals("BLOCKED", WorkOrderStatusEnum.BLOCKED.getCode());
        assertEquals("被阻塞", WorkOrderStatusEnum.BLOCKED.getInfo());
        
        // Test OVERDUE
        assertEquals("OVERDUE", WorkOrderStatusEnum.OVERDUE.getCode());
        assertEquals("已延期", WorkOrderStatusEnum.OVERDUE.getInfo());
    }

    @Test
    void testAllEnumValues() {
        WorkOrderStatusEnum[] values = WorkOrderStatusEnum.values();
        assertEquals(5, values.length);
        
        // Verify all expected values are present
        boolean hasNotStarted = false, hasInProgress = false, hasClosed = false, 
                hasBlocked = false, hasOverdue = false;
        
        for (WorkOrderStatusEnum status : values) {
            switch (status) {
                case NOT_STARTED:
                    hasNotStarted = true;
                    break;
                case IN_PROGRESS:
                    hasInProgress = true;
                    break;
                case CLOSED:
                    hasClosed = true;
                    break;
                case BLOCKED:
                    hasBlocked = true;
                    break;
                case OVERDUE:
                    hasOverdue = true;
                    break;
            }
        }
        
        assertTrue(hasNotStarted);
        assertTrue(hasInProgress);
        assertTrue(hasClosed);
        assertTrue(hasBlocked);
        assertTrue(hasOverdue);
    }
}
