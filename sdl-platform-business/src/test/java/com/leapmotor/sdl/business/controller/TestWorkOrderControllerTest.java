package com.leapmotor.sdl.business.controller;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.domain.WorkOrderLog;
import com.leapmotor.sdl.business.enums.WorkOrderStatusEnum;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;
import com.leapmotor.sdl.business.service.IWorkOrderLogService;

/**
 * 测试工单控制器集成测试
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@WebMvcTest(TestWorkOrderController.class)
class TestWorkOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ITestWorkOrderService testWorkOrderService;

    @MockBean
    private IWorkOrderLogService workOrderLogService;

    @Autowired
    private ObjectMapper objectMapper;

    private TestWorkOrder testWorkOrder;
    private WorkOrderLog workOrderLog;

    @BeforeEach
    void setUp() {
        testWorkOrder = new TestWorkOrder();
        testWorkOrder.setTicketId(1L);
        testWorkOrder.setTicketTitle("测试工单");
        testWorkOrder.setTicketStatus(WorkOrderStatusEnum.NOT_STARTED.getCode());
        testWorkOrder.setHandler("admin");
        testWorkOrder.setHandlerId(1L);
        testWorkOrder.setExpectFinishTime(new Date());

        workOrderLog = new WorkOrderLog();
        workOrderLog.setLogId(1L);
        workOrderLog.setTicketId(1L);
        workOrderLog.setOperationType("CREATE");
        workOrderLog.setAfterStatus(WorkOrderStatusEnum.NOT_STARTED.getCode());
        workOrderLog.setOperatorName("admin");
        workOrderLog.setOperationTime(new Date());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:list"})
    void testList() throws Exception {
        // Given
        List<TestWorkOrder> workOrders = Arrays.asList(testWorkOrder);
        when(testWorkOrderService.selectTestWorkOrderList(any(TestWorkOrder.class)))
            .thenReturn(workOrders);

        // When & Then
        mockMvc.perform(get("/business/workorder/list"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.rows").isArray())
            .andExpect(jsonPath("$.rows[0].ticketId").value(1))
            .andExpect(jsonPath("$.rows[0].ticketTitle").value("测试工单"));

        verify(testWorkOrderService).selectTestWorkOrderList(any(TestWorkOrder.class));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:query"})
    void testGetInfo() throws Exception {
        // Given
        when(testWorkOrderService.selectTestWorkOrderByTicketId(1L)).thenReturn(testWorkOrder);

        // When & Then
        mockMvc.perform(get("/business/workorder/1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.ticketId").value(1))
            .andExpect(jsonPath("$.data.ticketTitle").value("测试工单"));

        verify(testWorkOrderService).selectTestWorkOrderByTicketId(1L);
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:assign"})
    void testAssign() throws Exception {
        // Given
        when(testWorkOrderService.assignWorkOrder(anyLong(), anyLong(), anyString(), anyLong(), anyString()))
            .thenReturn(1);

        String requestBody = """
            {
                "ticketId": 1,
                "handlerId": 2,
                "handlerName": "testUser"
            }
            """;

        // When & Then
        mockMvc.perform(post("/business/workorder/assign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).assignWorkOrder(eq(1L), eq(2L), eq("testUser"), anyLong(), anyString());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:complete"})
    void testComplete() throws Exception {
        // Given
        when(testWorkOrderService.completeWorkOrder(anyLong(), anyLong(), anyString())).thenReturn(1);

        // When & Then
        mockMvc.perform(post("/business/workorder/complete/1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).completeWorkOrder(eq(1L), anyLong(), anyString());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:block"})
    void testBlock() throws Exception {
        // Given
        when(testWorkOrderService.blockWorkOrder(anyLong(), anyString(), anyLong(), anyString()))
            .thenReturn(1);

        String requestBody = """
            {
                "ticketId": 1,
                "reason": "测试阻塞原因"
            }
            """;

        // When & Then
        mockMvc.perform(post("/business/workorder/block")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).blockWorkOrder(eq(1L), eq("测试阻塞原因"), anyLong(), anyString());
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:query"})
    void testGetLogs() throws Exception {
        // Given
        List<WorkOrderLog> logs = Arrays.asList(workOrderLog);
        when(workOrderLogService.selectWorkOrderLogListByTicketId(1L)).thenReturn(logs);

        // When & Then
        mockMvc.perform(get("/business/workorder/logs/1"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data[0].logId").value(1))
            .andExpect(jsonPath("$.data[0].operationType").value("CREATE"));

        verify(workOrderLogService).selectWorkOrderLogListByTicketId(1L);
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:list"})
    void testOverdue() throws Exception {
        // Given
        List<TestWorkOrder> overdueWorkOrders = Arrays.asList(testWorkOrder);
        when(testWorkOrderService.selectOverdueWorkOrders()).thenReturn(overdueWorkOrders);

        // When & Then
        mockMvc.perform(get("/business/workorder/overdue"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.rows").isArray())
            .andExpect(jsonPath("$.rows[0].ticketId").value(1));

        verify(testWorkOrderService).selectOverdueWorkOrders();
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:add"})
    void testAdd() throws Exception {
        // Given
        when(testWorkOrderService.insertTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);

        String requestBody = objectMapper.writeValueAsString(testWorkOrder);

        // When & Then
        mockMvc.perform(post("/business/workorder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).insertTestWorkOrder(any(TestWorkOrder.class));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:edit"})
    void testEdit() throws Exception {
        // Given
        when(testWorkOrderService.updateTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);

        String requestBody = objectMapper.writeValueAsString(testWorkOrder);

        // When & Then
        mockMvc.perform(put("/business/workorder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).updateTestWorkOrder(any(TestWorkOrder.class));
    }

    @Test
    @WithMockUser(username = "admin", authorities = {"business:workorder:remove"})
    void testRemove() throws Exception {
        // Given
        when(testWorkOrderService.deleteTestWorkOrderByTicketIds(any(Long[].class))).thenReturn(1);

        // When & Then
        mockMvc.perform(delete("/business/workorder/1,2"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200));

        verify(testWorkOrderService).deleteTestWorkOrderByTicketIds(any(Long[].class));
    }
}
