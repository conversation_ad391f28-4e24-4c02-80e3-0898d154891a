package com.leapmotor.sdl.business.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.enums.WorkOrderStatusEnum;
import com.leapmotor.sdl.business.mapper.TestWorkOrderMapper;
import com.leapmotor.sdl.business.service.impl.TestWorkOrderServiceImpl;
import com.leapmotor.sdl.system.service.ISysConfigService;
import com.leapmotor.sdl.system.service.ISysUserService;

/**
 * 测试工单服务单元测试
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@ExtendWith(MockitoExtension.class)
class TestWorkOrderServiceTest {

    @Mock
    private TestWorkOrderMapper testWorkOrderMapper;

    @Mock
    private IWorkOrderLogService workOrderLogService;

    @Mock
    private ISysConfigService configService;

    @Mock
    private ISysUserService userService;

    @InjectMocks
    private TestWorkOrderServiceImpl testWorkOrderService;

    private TestWorkOrder testWorkOrder;

    @BeforeEach
    void setUp() {
        testWorkOrder = new TestWorkOrder();
        testWorkOrder.setTicketId(1L);
        testWorkOrder.setTicketTitle("测试工单");
        testWorkOrder.setTicketStatus(WorkOrderStatusEnum.NOT_STARTED.getCode());
        testWorkOrder.setHandler("admin");
        testWorkOrder.setHandlerId(1L);
        testWorkOrder.setExpectFinishTime(new Date());
    }

    @Test
    void testInsertTestWorkOrder() {
        // Given
        when(configService.selectConfigByKey("workorder.default.assignee")).thenReturn("admin");
        when(testWorkOrderMapper.insertTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);
        doNothing().when(workOrderLogService).recordWorkOrderLog(anyLong(), anyString(), 
            anyString(), anyString(), anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), anyString(), anyLong());

        TestWorkOrder newWorkOrder = new TestWorkOrder();
        newWorkOrder.setTicketTitle("新测试工单");

        // When
        int result = testWorkOrderService.insertTestWorkOrder(newWorkOrder);

        // Then
        assertEquals(1, result);
        assertEquals(WorkOrderStatusEnum.NOT_STARTED.getCode(), newWorkOrder.getTicketStatus());
        verify(testWorkOrderMapper).insertTestWorkOrder(newWorkOrder);
        verify(workOrderLogService).recordWorkOrderLog(anyLong(), eq("CREATE"), 
            isNull(), eq(WorkOrderStatusEnum.NOT_STARTED.getCode()), 
            isNull(), anyString(), isNull(), anyLong(), 
            anyString(), anyString(), isNull());
    }

    @Test
    void testAssignWorkOrder() {
        // Given
        when(testWorkOrderMapper.selectTestWorkOrderByTicketId(1L)).thenReturn(testWorkOrder);
        when(testWorkOrderMapper.updateTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);
        doNothing().when(workOrderLogService).recordWorkOrderLog(anyLong(), anyString(), 
            anyString(), anyString(), anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), anyString(), anyLong());

        // When
        int result = testWorkOrderService.assignWorkOrder(1L, 2L, "testUser", 1L, "admin");

        // Then
        assertEquals(1, result);
        assertEquals(WorkOrderStatusEnum.IN_PROGRESS.getCode(), testWorkOrder.getTicketStatus());
        assertEquals("testUser", testWorkOrder.getHandler());
        assertEquals(2L, testWorkOrder.getHandlerId());
        verify(testWorkOrderMapper).updateTestWorkOrder(testWorkOrder);
        verify(workOrderLogService).recordWorkOrderLog(eq(1L), eq("ASSIGN"), 
            eq(WorkOrderStatusEnum.NOT_STARTED.getCode()), eq(WorkOrderStatusEnum.IN_PROGRESS.getCode()), 
            eq("admin"), eq("testUser"), eq(1L), eq(2L), 
            anyString(), eq("admin"), eq(1L));
    }

    @Test
    void testAssignWorkOrderInvalidStatus() {
        // Given
        testWorkOrder.setTicketStatus(WorkOrderStatusEnum.CLOSED.getCode());
        when(testWorkOrderMapper.selectTestWorkOrderByTicketId(1L)).thenReturn(testWorkOrder);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            testWorkOrderService.assignWorkOrder(1L, 2L, "testUser", 1L, "admin");
        });
    }

    @Test
    void testCompleteWorkOrder() {
        // Given
        testWorkOrder.setTicketStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        when(testWorkOrderMapper.selectTestWorkOrderByTicketId(1L)).thenReturn(testWorkOrder);
        when(testWorkOrderMapper.updateTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);
        doNothing().when(workOrderLogService).recordWorkOrderLog(anyLong(), anyString(), 
            anyString(), anyString(), anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), anyString(), anyLong());

        // When
        int result = testWorkOrderService.completeWorkOrder(1L, 1L, "admin");

        // Then
        assertEquals(1, result);
        assertEquals(WorkOrderStatusEnum.CLOSED.getCode(), testWorkOrder.getTicketStatus());
        assertNotNull(testWorkOrder.getActualFinishTime());
        verify(testWorkOrderMapper).updateTestWorkOrder(testWorkOrder);
        verify(workOrderLogService).recordWorkOrderLog(eq(1L), eq("COMPLETE"), 
            eq(WorkOrderStatusEnum.IN_PROGRESS.getCode()), eq(WorkOrderStatusEnum.CLOSED.getCode()), 
            eq("admin"), eq("admin"), eq(1L), eq(1L), 
            eq("完成工单"), eq("admin"), eq(1L));
    }

    @Test
    void testBlockWorkOrder() {
        // Given
        testWorkOrder.setTicketStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        when(testWorkOrderMapper.selectTestWorkOrderByTicketId(1L)).thenReturn(testWorkOrder);
        when(testWorkOrderMapper.updateTestWorkOrder(any(TestWorkOrder.class))).thenReturn(1);
        doNothing().when(workOrderLogService).recordWorkOrderLog(anyLong(), anyString(), 
            anyString(), anyString(), anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), anyString(), anyLong());

        // When
        int result = testWorkOrderService.blockWorkOrder(1L, "测试阻塞", 1L, "admin");

        // Then
        assertEquals(1, result);
        assertEquals(WorkOrderStatusEnum.BLOCKED.getCode(), testWorkOrder.getTicketStatus());
        verify(testWorkOrderMapper).updateTestWorkOrder(testWorkOrder);
        verify(workOrderLogService).recordWorkOrderLog(eq(1L), eq("BLOCK"), 
            eq(WorkOrderStatusEnum.IN_PROGRESS.getCode()), eq(WorkOrderStatusEnum.BLOCKED.getCode()), 
            eq("admin"), eq("admin"), eq(1L), eq(1L), 
            eq("阻塞工单，原因：测试阻塞"), eq("admin"), eq(1L));
    }

    @Test
    void testCheckAndMarkOverdueWorkOrders() {
        // Given
        TestWorkOrder overdueWorkOrder1 = new TestWorkOrder();
        overdueWorkOrder1.setTicketId(1L);
        overdueWorkOrder1.setTicketStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        overdueWorkOrder1.setExpectFinishTime(new Date(System.currentTimeMillis() - 86400000)); // 昨天

        TestWorkOrder overdueWorkOrder2 = new TestWorkOrder();
        overdueWorkOrder2.setTicketId(2L);
        overdueWorkOrder2.setTicketStatus(WorkOrderStatusEnum.NOT_STARTED.getCode());
        overdueWorkOrder2.setExpectFinishTime(new Date(System.currentTimeMillis() - 86400000)); // 昨天

        List<TestWorkOrder> overdueWorkOrders = Arrays.asList(overdueWorkOrder1, overdueWorkOrder2);

        when(testWorkOrderMapper.selectOverdueWorkOrders()).thenReturn(overdueWorkOrders);
        when(testWorkOrderMapper.batchUpdateStatusToOverdue(any(Long[].class))).thenReturn(2);
        doNothing().when(workOrderLogService).recordWorkOrderLog(anyLong(), anyString(), 
            anyString(), anyString(), anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), anyString(), anyLong());

        // When
        int result = testWorkOrderService.checkAndMarkOverdueWorkOrders();

        // Then
        assertEquals(2, result);
        verify(testWorkOrderMapper).selectOverdueWorkOrders();
        verify(testWorkOrderMapper).batchUpdateStatusToOverdue(new Long[]{1L, 2L});
        verify(workOrderLogService, times(2)).recordWorkOrderLog(anyLong(), eq("OVERDUE"), 
            anyString(), eq(WorkOrderStatusEnum.OVERDUE.getCode()), 
            anyString(), anyString(), anyLong(), anyLong(), 
            anyString(), eq("系统"), isNull());
    }

    @Test
    void testSelectOverdueWorkOrders() {
        // Given
        List<TestWorkOrder> overdueWorkOrders = Arrays.asList(testWorkOrder);
        when(testWorkOrderMapper.selectOverdueWorkOrders()).thenReturn(overdueWorkOrders);

        // When
        List<TestWorkOrder> result = testWorkOrderService.selectOverdueWorkOrders();

        // Then
        assertEquals(1, result.size());
        assertEquals(testWorkOrder, result.get(0));
        verify(testWorkOrderMapper).selectOverdueWorkOrders();
    }
}
