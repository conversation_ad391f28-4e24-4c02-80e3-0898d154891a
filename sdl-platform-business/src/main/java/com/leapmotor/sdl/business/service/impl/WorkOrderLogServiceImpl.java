package com.leapmotor.sdl.business.service.impl;

import java.util.Date;
import java.util.List;
import com.leapmotor.sdl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.leapmotor.sdl.business.mapper.WorkOrderLogMapper;
import com.leapmotor.sdl.business.domain.WorkOrderLog;
import com.leapmotor.sdl.business.service.IWorkOrderLogService;

/**
 * 工单日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class WorkOrderLogServiceImpl implements IWorkOrderLogService 
{
    @Autowired
    private WorkOrderLogMapper workOrderLogMapper;

    /**
     * 查询工单日志
     * 
     * @param logId 工单日志主键
     * @return 工单日志
     */
    @Override
    public WorkOrderLog selectWorkOrderLogByLogId(Long logId)
    {
        return workOrderLogMapper.selectWorkOrderLogByLogId(logId);
    }

    /**
     * 查询工单日志列表
     * 
     * @param workOrderLog 工单日志
     * @return 工单日志
     */
    @Override
    public List<WorkOrderLog> selectWorkOrderLogList(WorkOrderLog workOrderLog)
    {
        return workOrderLogMapper.selectWorkOrderLogList(workOrderLog);
    }

    /**
     * 根据工单ID查询工单日志列表
     * 
     * @param ticketId 工单ID
     * @return 工单日志集合
     */
    @Override
    public List<WorkOrderLog> selectWorkOrderLogListByTicketId(Long ticketId)
    {
        return workOrderLogMapper.selectWorkOrderLogListByTicketId(ticketId);
    }

    /**
     * 新增工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    @Override
    public int insertWorkOrderLog(WorkOrderLog workOrderLog)
    {
        workOrderLog.setCreateTime(DateUtils.getNowDate());
        return workOrderLogMapper.insertWorkOrderLog(workOrderLog);
    }

    /**
     * 修改工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    @Override
    public int updateWorkOrderLog(WorkOrderLog workOrderLog)
    {
        workOrderLog.setUpdateTime(DateUtils.getNowDate());
        return workOrderLogMapper.updateWorkOrderLog(workOrderLog);
    }

    /**
     * 批量删除工单日志
     * 
     * @param logIds 需要删除的工单日志主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderLogByLogIds(Long[] logIds)
    {
        return workOrderLogMapper.deleteWorkOrderLogByLogIds(logIds);
    }

    /**
     * 删除工单日志信息
     * 
     * @param logId 工单日志主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderLogByLogId(Long logId)
    {
        return workOrderLogMapper.deleteWorkOrderLogByLogId(logId);
    }

    /**
     * 记录工单操作日志
     * 
     * @param ticketId 工单ID
     * @param operationType 操作类型
     * @param beforeStatus 操作前状态
     * @param afterStatus 操作后状态
     * @param beforeHandler 操作前处理人
     * @param afterHandler 操作后处理人
     * @param beforeHandlerId 操作前处理人ID
     * @param afterHandlerId 操作后处理人ID
     * @param operationDesc 操作描述
     * @param operatorName 操作人
     * @param operatorId 操作人ID
     */
    @Override
    public void recordWorkOrderLog(Long ticketId, String operationType, String beforeStatus, String afterStatus,
                                   String beforeHandler, String afterHandler, Long beforeHandlerId, Long afterHandlerId,
                                   String operationDesc, String operatorName, Long operatorId)
    {
        WorkOrderLog log = new WorkOrderLog();
        log.setTicketId(ticketId);
        log.setOperationType(operationType);
        log.setBeforeStatus(beforeStatus);
        log.setAfterStatus(afterStatus);
        log.setBeforeHandler(beforeHandler);
        log.setAfterHandler(afterHandler);
        log.setBeforeHandlerId(beforeHandlerId);
        log.setAfterHandlerId(afterHandlerId);
        log.setOperationDesc(operationDesc);
        log.setOperatorName(operatorName);
        log.setOperatorId(operatorId);
        log.setOperationTime(new Date());
        log.setCreateBy(operatorName);
        
        insertWorkOrderLog(log);
    }
}
