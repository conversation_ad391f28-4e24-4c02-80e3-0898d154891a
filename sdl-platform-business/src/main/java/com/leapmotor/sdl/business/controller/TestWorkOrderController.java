package com.leapmotor.sdl.business.controller;

import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.leapmotor.sdl.common.annotation.Log;
import com.leapmotor.sdl.common.core.controller.BaseController;
import com.leapmotor.sdl.common.core.domain.AjaxResult;
import com.leapmotor.sdl.common.enums.BusinessType;
import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;
import com.leapmotor.sdl.business.service.IWorkOrderLogService;
import com.leapmotor.sdl.common.utils.poi.ExcelUtil;
import com.leapmotor.sdl.common.core.page.TableDataInfo;
import com.leapmotor.sdl.common.utils.SecurityUtils;

/**
 * 测试工单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/business/workorder")
public class TestWorkOrderController extends BaseController
{
    @Autowired
    private ITestWorkOrderService testWorkOrderService;

    @Autowired
    private IWorkOrderLogService workOrderLogService;

    /**
     * 查询测试工单列表
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestWorkOrder testWorkOrder)
    {
        startPage();
        List<TestWorkOrder> list = testWorkOrderService.selectTestWorkOrderList(testWorkOrder);
        return getDataTable(list);
    }

    /**
     * 导出测试工单列表
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:export')")
    @Log(title = "测试工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestWorkOrder testWorkOrder)
    {
        List<TestWorkOrder> list = testWorkOrderService.selectTestWorkOrderList(testWorkOrder);
        ExcelUtil<TestWorkOrder> util = new ExcelUtil<TestWorkOrder>(TestWorkOrder.class);
        util.exportExcel(response, list, "测试工单数据");
    }

    /**
     * 获取测试工单详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:query')")
    @GetMapping(value = "/{ticketId}")
    public AjaxResult getInfo(@PathVariable("ticketId") Long ticketId)
    {
        return success(testWorkOrderService.selectTestWorkOrderByTicketId(ticketId));
    }

    /**
     * 新增测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:add')")
    @Log(title = "测试工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestWorkOrder testWorkOrder)
    {
        return toAjax(testWorkOrderService.insertTestWorkOrder(testWorkOrder));
    }

    /**
     * 修改测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:edit')")
    @Log(title = "测试工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestWorkOrder testWorkOrder)
    {
        return toAjax(testWorkOrderService.updateTestWorkOrder(testWorkOrder));
    }

    /**
     * 删除测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:remove')")
    @Log(title = "测试工单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ticketIds}")
    public AjaxResult remove(@PathVariable Long[] ticketIds)
    {
        return toAjax(testWorkOrderService.deleteTestWorkOrderByTicketIds(ticketIds));
    }

    /**
     * 指派工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:assign')")
    @Log(title = "指派工单", businessType = BusinessType.UPDATE)
    @PostMapping("/assign")
    public AjaxResult assign(@RequestBody Map<String, Object> params)
    {
        Long ticketId = Long.valueOf(params.get("ticketId").toString());
        Long handlerId = Long.valueOf(params.get("handlerId").toString());
        String handlerName = params.get("handlerName").toString();

        Long operatorId = SecurityUtils.getUserId();
        String operatorName = SecurityUtils.getUsername();

        return toAjax(testWorkOrderService.assignWorkOrder(ticketId, handlerId, handlerName, operatorId, operatorName));
    }

    /**
     * 完成工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:complete')")
    @Log(title = "完成工单", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{ticketId}")
    public AjaxResult complete(@PathVariable Long ticketId)
    {
        Long operatorId = SecurityUtils.getUserId();
        String operatorName = SecurityUtils.getUsername();

        return toAjax(testWorkOrderService.completeWorkOrder(ticketId, operatorId, operatorName));
    }

    /**
     * 阻塞工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:block')")
    @Log(title = "阻塞工单", businessType = BusinessType.UPDATE)
    @PostMapping("/block")
    public AjaxResult block(@RequestBody Map<String, Object> params)
    {
        Long ticketId = Long.valueOf(params.get("ticketId").toString());
        String reason = params.get("reason").toString();

        Long operatorId = SecurityUtils.getUserId();
        String operatorName = SecurityUtils.getUsername();

        return toAjax(testWorkOrderService.blockWorkOrder(ticketId, reason, operatorId, operatorName));
    }

    /**
     * 获取工单日志
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:query')")
    @GetMapping("/logs/{ticketId}")
    public AjaxResult getLogs(@PathVariable Long ticketId)
    {
        return success(workOrderLogService.selectWorkOrderLogListByTicketId(ticketId));
    }

    /**
     * 查询延期工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:list')")
    @GetMapping("/overdue")
    public TableDataInfo overdue()
    {
        List<TestWorkOrder> list = testWorkOrderService.selectOverdueWorkOrders();
        return getDataTable(list);
    }
}
