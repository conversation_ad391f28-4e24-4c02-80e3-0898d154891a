package com.leapmotor.sdl.business.service;

import java.util.List;
import com.leapmotor.sdl.business.domain.WorkOrderLog;

/**
 * 工单日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IWorkOrderLogService 
{
    /**
     * 查询工单日志
     * 
     * @param logId 工单日志主键
     * @return 工单日志
     */
    public WorkOrderLog selectWorkOrderLogByLogId(Long logId);

    /**
     * 查询工单日志列表
     * 
     * @param workOrderLog 工单日志
     * @return 工单日志集合
     */
    public List<WorkOrderLog> selectWorkOrderLogList(WorkOrderLog workOrderLog);

    /**
     * 根据工单ID查询工单日志列表
     * 
     * @param ticketId 工单ID
     * @return 工单日志集合
     */
    public List<WorkOrderLog> selectWorkOrderLogListByTicketId(Long ticketId);

    /**
     * 新增工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    public int insertWorkOrderLog(WorkOrderLog workOrderLog);

    /**
     * 修改工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    public int updateWorkOrderLog(WorkOrderLog workOrderLog);

    /**
     * 批量删除工单日志
     * 
     * @param logIds 需要删除的工单日志主键集合
     * @return 结果
     */
    public int deleteWorkOrderLogByLogIds(Long[] logIds);

    /**
     * 删除工单日志信息
     * 
     * @param logId 工单日志主键
     * @return 结果
     */
    public int deleteWorkOrderLogByLogId(Long logId);

    /**
     * 记录工单操作日志
     * 
     * @param ticketId 工单ID
     * @param operationType 操作类型
     * @param beforeStatus 操作前状态
     * @param afterStatus 操作后状态
     * @param beforeHandler 操作前处理人
     * @param afterHandler 操作后处理人
     * @param beforeHandlerId 操作前处理人ID
     * @param afterHandlerId 操作后处理人ID
     * @param operationDesc 操作描述
     * @param operatorName 操作人
     * @param operatorId 操作人ID
     */
    public void recordWorkOrderLog(Long ticketId, String operationType, String beforeStatus, String afterStatus,
                                   String beforeHandler, String afterHandler, Long beforeHandlerId, Long afterHandlerId,
                                   String operationDesc, String operatorName, Long operatorId);
}
