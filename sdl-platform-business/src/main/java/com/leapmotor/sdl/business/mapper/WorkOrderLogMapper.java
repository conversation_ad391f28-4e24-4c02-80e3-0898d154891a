package com.leapmotor.sdl.business.mapper;

import java.util.List;
import com.leapmotor.sdl.business.domain.WorkOrderLog;

/**
 * 工单日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface WorkOrderLogMapper 
{
    /**
     * 查询工单日志
     * 
     * @param logId 工单日志主键
     * @return 工单日志
     */
    public WorkOrderLog selectWorkOrderLogByLogId(Long logId);

    /**
     * 查询工单日志列表
     * 
     * @param workOrderLog 工单日志
     * @return 工单日志集合
     */
    public List<WorkOrderLog> selectWorkOrderLogList(WorkOrderLog workOrderLog);

    /**
     * 根据工单ID查询工单日志列表
     * 
     * @param ticketId 工单ID
     * @return 工单日志集合
     */
    public List<WorkOrderLog> selectWorkOrderLogListByTicketId(Long ticketId);

    /**
     * 新增工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    public int insertWorkOrderLog(WorkOrderLog workOrderLog);

    /**
     * 修改工单日志
     * 
     * @param workOrderLog 工单日志
     * @return 结果
     */
    public int updateWorkOrderLog(WorkOrderLog workOrderLog);

    /**
     * 删除工单日志
     * 
     * @param logId 工单日志主键
     * @return 结果
     */
    public int deleteWorkOrderLogByLogId(Long logId);

    /**
     * 批量删除工单日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderLogByLogIds(Long[] logIds);
}
