package com.leapmotor.sdl.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.leapmotor.sdl.common.annotation.Excel;
import com.leapmotor.sdl.common.core.domain.BaseEntity;

/**
 * 工单日志对象 sdl_workorder_logs
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public class WorkOrderLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 工单ID */
    @Excel(name = "工单ID")
    private Long ticketId;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operationType;

    /** 操作前状态 */
    @Excel(name = "操作前状态")
    private String beforeStatus;

    /** 操作后状态 */
    @Excel(name = "操作后状态")
    private String afterStatus;

    /** 操作前处理人 */
    @Excel(name = "操作前处理人")
    private String beforeHandler;

    /** 操作后处理人 */
    @Excel(name = "操作后处理人")
    private String afterHandler;

    /** 操作前处理人ID */
    private Long beforeHandlerId;

    /** 操作后处理人ID */
    private Long afterHandlerId;

    /** 操作描述 */
    @Excel(name = "操作描述")
    private String operationDesc;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operatorName;

    /** 操作人ID */
    private Long operatorId;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }

    public void setTicketId(Long ticketId) 
    {
        this.ticketId = ticketId;
    }

    public Long getTicketId() 
    {
        return ticketId;
    }

    public void setOperationType(String operationType) 
    {
        this.operationType = operationType;
    }

    public String getOperationType() 
    {
        return operationType;
    }

    public void setBeforeStatus(String beforeStatus) 
    {
        this.beforeStatus = beforeStatus;
    }

    public String getBeforeStatus() 
    {
        return beforeStatus;
    }

    public void setAfterStatus(String afterStatus) 
    {
        this.afterStatus = afterStatus;
    }

    public String getAfterStatus() 
    {
        return afterStatus;
    }

    public void setBeforeHandler(String beforeHandler) 
    {
        this.beforeHandler = beforeHandler;
    }

    public String getBeforeHandler() 
    {
        return beforeHandler;
    }

    public void setAfterHandler(String afterHandler) 
    {
        this.afterHandler = afterHandler;
    }

    public String getAfterHandler() 
    {
        return afterHandler;
    }

    public void setBeforeHandlerId(Long beforeHandlerId)
    {
        this.beforeHandlerId = beforeHandlerId;
    }

    public Long getBeforeHandlerId()
    {
        return beforeHandlerId;
    }

    public void setAfterHandlerId(Long afterHandlerId)
    {
        this.afterHandlerId = afterHandlerId;
    }

    public Long getAfterHandlerId()
    {
        return afterHandlerId;
    }

    public void setOperationDesc(String operationDesc) 
    {
        this.operationDesc = operationDesc;
    }

    public String getOperationDesc() 
    {
        return operationDesc;
    }

    public void setOperatorName(String operatorName) 
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName() 
    {
        return operatorName;
    }

    public void setOperatorId(Long operatorId)
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId()
    {
        return operatorId;
    }

    public void setOperationTime(Date operationTime) 
    {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() 
    {
        return operationTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("ticketId", getTicketId())
            .append("operationType", getOperationType())
            .append("beforeStatus", getBeforeStatus())
            .append("afterStatus", getAfterStatus())
            .append("beforeHandler", getBeforeHandler())
            .append("afterHandler", getAfterHandler())
            .append("beforeHandlerId", getBeforeHandlerId())
            .append("afterHandlerId", getAfterHandlerId())
            .append("operationDesc", getOperationDesc())
            .append("operatorName", getOperatorName())
            .append("operatorId", getOperatorId())
            .append("operationTime", getOperationTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
