package com.leapmotor.sdl.business.enums;

/**
 * 工单状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public enum WorkOrderStatusEnum 
{
    /**
     * 未开始
     */
    NOT_STARTED("NOT_STARTED", "未开始"),
    
    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    
    /**
     * 已关闭
     */
    CLOSED("CLOSED", "已关闭"),
    
    /**
     * 被阻塞
     */
    BLOCKED("BLOCKED", "被阻塞"),
    
    /**
     * 已延期
     */
    OVERDUE("OVERDUE", "已延期");

    private final String code;
    private final String info;

    WorkOrderStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 枚举
     */
    public static WorkOrderStatusEnum getByCode(String code)
    {
        for (WorkOrderStatusEnum status : values())
        {
            if (status.getCode().equals(code))
            {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态是否可以转换
     * 
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否可以转换
     */
    public static boolean canTransition(String fromStatus, String toStatus)
    {
        WorkOrderStatusEnum from = getByCode(fromStatus);
        WorkOrderStatusEnum to = getByCode(toStatus);
        
        if (from == null || to == null)
        {
            return false;
        }
        
        // 任何状态都可以转为被阻塞
        if (to == BLOCKED)
        {
            return true;
        }
        
        // 被阻塞状态可以转为任何状态（除了已延期，延期由系统自动设置）
        if (from == BLOCKED && to != OVERDUE)
        {
            return true;
        }
        
        // 正常状态流转
        switch (from)
        {
            case NOT_STARTED:
                return to == IN_PROGRESS;
            case IN_PROGRESS:
                return to == CLOSED || to == OVERDUE;
            case CLOSED:
                return false; // 已关闭状态不能再转换
            case OVERDUE:
                return to == CLOSED; // 延期状态只能转为已关闭
            default:
                return false;
        }
    }
}
