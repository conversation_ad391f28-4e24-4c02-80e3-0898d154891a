package com.leapmotor.sdl.business.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;
import com.leapmotor.sdl.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 工单相关定时任务
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Component("workOrderTask")
public class WorkOrderTask
{
    private static final Logger log = LoggerFactory.getLogger(WorkOrderTask.class);

    @Autowired
    private ITestWorkOrderService testWorkOrderService;

    /**
     * 检查并标记延期工单
     * 每日0点执行
     */
    public void checkOverdueWorkOrders()
    {
        log.info("开始执行工单延期检查任务");
        
        try
        {
            int overdueCount = testWorkOrderService.checkAndMarkOverdueWorkOrders();
            log.info("工单延期检查任务执行完成，共标记 {} 个延期工单", overdueCount);
        }
        catch (Exception e)
        {
            log.error("工单延期检查任务执行失败", e);
        }
    }

    /**
     * 检查并标记延期工单（带参数）
     * 
     * @param params 参数（预留）
     */
    public void checkOverdueWorkOrders(String params)
    {
        log.info("开始执行工单延期检查任务，参数：{}", params);
        
        try
        {
            int overdueCount = testWorkOrderService.checkAndMarkOverdueWorkOrders();
            log.info("工单延期检查任务执行完成，共标记 {} 个延期工单", overdueCount);
            
            if (StringUtils.isNotEmpty(params))
            {
                log.info("任务参数：{}", params);
            }
        }
        catch (Exception e)
        {
            log.error("工单延期检查任务执行失败", e);
        }
    }

    /**
     * 工单统计任务
     * 用于统计各种状态的工单数量
     */
    public void workOrderStatistics()
    {
        log.info("开始执行工单统计任务");
        
        try
        {
            // 这里可以添加工单统计逻辑
            // 比如统计各状态工单数量，发送报告等
            log.info("工单统计任务执行完成");
        }
        catch (Exception e)
        {
            log.error("工单统计任务执行失败", e);
        }
    }
}
