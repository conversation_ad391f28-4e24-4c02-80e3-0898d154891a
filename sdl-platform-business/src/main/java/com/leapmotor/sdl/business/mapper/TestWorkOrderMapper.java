package com.leapmotor.sdl.business.mapper;

import java.util.List;
import com.leapmotor.sdl.business.domain.TestWorkOrder;

/**
 * 测试工单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface TestWorkOrderMapper 
{
    /**
     * 查询测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 测试工单
     */
    public TestWorkOrder selectTestWorkOrderByTicketId(Long ticketId);

    /**
     * 查询测试工单列表
     * 
     * @param testWorkOrder 测试工单
     * @return 测试工单集合
     */
    public List<TestWorkOrder> selectTestWorkOrderList(TestWorkOrder testWorkOrder);

    /**
     * 新增测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int insertTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 修改测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int updateTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 删除测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketId(Long ticketId);

    /**
     * 批量删除测试工单
     *
     * @param ticketIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketIds(Long[] ticketIds);

    /**
     * 查询延期工单列表
     *
     * @return 延期工单集合
     */
    public List<TestWorkOrder> selectOverdueWorkOrders();

    /**
     * 批量更新工单状态为延期
     *
     * @param ticketIds 工单ID集合
     * @return 结果
     */
    public int batchUpdateStatusToOverdue(Long[] ticketIds);
}
