package com.leapmotor.sdl.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.leapmotor.sdl.common.annotation.Excel;
import com.leapmotor.sdl.common.core.domain.BaseEntity;

/**
 * 测试工单对象 sdl_test_workorder
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public class TestWorkOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 工单ID */
    private Long ticketId;

    /** 工单标题 */
    @Excel(name = "工单标题")
    private String ticketTitle;

    /** 测试类型 */
    @Excel(name = "测试类型")
    private String ticketType;

    /** 所属项目 */
    @Excel(name = "所属项目")
    private String projectName;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 系统类型 */
    @Excel(name = "系统类型")
    private String systemType;

    /** 项目经理 */
    @Excel(name = "项目经理")
    private String projectManager;

    /** 软件代表 */
    @Excel(name = "软件代表")
    private String softwareRepresentative;

    /** 测试申请人 */
    @Excel(name = "测试申请人")
    private String testApplicant;

    /** 安全对接人 */
    @Excel(name = "安全对接人")
    private String securityRepresentative;

    /** 前端开发 */
    @Excel(name = "前端开发")
    private String frontendDevelopment;

    /** 后端开发 */
    @Excel(name = "后端开发")
    private String backendDevelopment;

    /** 当前处理人 */
    @Excel(name = "当前处理人")
    private String handler;

    /** 当前处理人ID */
    private Long handlerId;

    /** 测试环境地址 */
    @Excel(name = "测试环境地址")
    private String testEnv;

    /** 测试账号密码 */
    @Excel(name = "测试账号密码")
    private String testAccount;

    /** 代码仓库地址 */
    @Excel(name = "代码仓库地址")
    private String codeRepo;

    /** 测试范围 */
    @Excel(name = "测试范围")
    private String trestScope;

    /** 附件信息 */
    @Excel(name = "附件信息")
    private String attachment;

    /** 期望完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expectFinishTime;

    /** 实际完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualFinishTime;

    /** 优先级 */
    @Excel(name = "优先级")
    private String priority;

    /** 工单状态 */
    @Excel(name = "工单状态")
    private String ticketStatus;

    /** 开始创建时间 */
    private String startCreateTime;

    /** 结束创建时间 */
    private String endCreateTime;

    public void setTicketId(Long ticketId) 
    {
        this.ticketId = ticketId;
    }

    public Long getTicketId() 
    {
        return ticketId;
    }

    public void setTicketTitle(String ticketTitle) 
    {
        this.ticketTitle = ticketTitle;
    }

    public String getTicketTitle() 
    {
        return ticketTitle;
    }

    public void setTicketType(String ticketType) 
    {
        this.ticketType = ticketType;
    }

    public String getTicketType() 
    {
        return ticketType;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setSystemName(String systemName) 
    {
        this.systemName = systemName;
    }

    public String getSystemName()
    {
        return systemName;
    }

    public void setSystemType(String systemType)
    {
        this.systemType = systemType;
    }

    public String getSystemType()
    {
        return systemType;
    }

    public void setProjectManager(String projectManager) 
    {
        this.projectManager = projectManager;
    }

    public String getProjectManager() 
    {
        return projectManager;
    }

    public void setSoftwareRepresentative(String softwareRepresentative)
    {
        this.softwareRepresentative = softwareRepresentative;
    }

    public String getSoftwareRepresentative()
    {
        return softwareRepresentative;
    }

    public void setTestApplicant(String testApplicant) 
    {
        this.testApplicant = testApplicant;
    }

    public String getTestApplicant() 
    {
        return testApplicant;
    }

    public void setSecurityRepresentative(String securityRepresentative) 
    {
        this.securityRepresentative = securityRepresentative;
    }

    public String getSecurityRepresentative()
    {
        return securityRepresentative;
    }

    public void setFrontendDevelopment(String frontendDevelopment)
    {
        this.frontendDevelopment = frontendDevelopment;
    }

    public String getFrontendDevelopment()
    {
        return frontendDevelopment;
    }

    public void setBackendDevelopment(String backendDevelopment)
    {
        this.backendDevelopment = backendDevelopment;
    }

    public String getBackendDevelopment()
    {
        return backendDevelopment;
    }

    public void setHandler(String handler)
    {
        this.handler = handler;
    }

    public String getHandler()
    {
        return handler;
    }

    public void setHandlerId(Long handlerId)
    {
        this.handlerId = handlerId;
    }

    public Long getHandlerId()
    {
        return handlerId;
    }

    public void setTestEnv(String testEnv) 
    {
        this.testEnv = testEnv;
    }

    public String getTestEnv() 
    {
        return testEnv;
    }

    public void setTestAccount(String testAccount) 
    {
        this.testAccount = testAccount;
    }

    public String getTestAccount() 
    {
        return testAccount;
    }

    public void setCodeRepo(String codeRepo) 
    {
        this.codeRepo = codeRepo;
    }

    public String getCodeRepo() 
    {
        return codeRepo;
    }

    public void setTrestScope(String trestScope) 
    {
        this.trestScope = trestScope;
    }

    public String getTrestScope() 
    {
        return trestScope;
    }

    public void setAttachment(String attachment) 
    {
        this.attachment = attachment;
    }

    public String getAttachment() 
    {
        return attachment;
    }

    public void setExpectFinishTime(Date expectFinishTime) 
    {
        this.expectFinishTime = expectFinishTime;
    }

    public Date getExpectFinishTime() 
    {
        return expectFinishTime;
    }

    public void setActualFinishTime(Date actualFinishTime) 
    {
        this.actualFinishTime = actualFinishTime;
    }

    public Date getActualFinishTime() 
    {
        return actualFinishTime;
    }

    public void setPriority(String priority) 
    {
        this.priority = priority;
    }

    public String getPriority() 
    {
        return priority;
    }

    public void setTicketStatus(String ticketStatus) 
    {
        this.ticketStatus = ticketStatus;
    }

    public String getTicketStatus() 
    {
        return ticketStatus;
    }

    public void setStartCreateTime(String startCreateTime) 
    {
        this.startCreateTime = startCreateTime;
    }

    public String getStartCreateTime() 
    {
        return startCreateTime;
    }

    public void setEndCreateTime(String endCreateTime) 
    {
        this.endCreateTime = endCreateTime;
    }

    public String getEndCreateTime() 
    {
        return endCreateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ticketId", getTicketId())
            .append("ticketTitle", getTicketTitle())
            .append("ticketType", getTicketType())
            .append("projectName", getProjectName())
            .append("systemName", getSystemName())
            .append("systemType", getSystemType())
            .append("projectManager", getProjectManager())
            .append("softwareRepresentative", getSoftwareRepresentative())
            .append("testApplicant", getTestApplicant())
            .append("securityRepresentative", getSecurityRepresentative())
            .append("frontendDevelopment", getFrontendDevelopment())
            .append("backendDevelopment", getBackendDevelopment())
            .append("handler", getHandler())
            .append("testEnv", getTestEnv())
            .append("testAccount", getTestAccount())
            .append("codeRepo", getCodeRepo())
            .append("trestScope", getTrestScope())
            .append("attachment", getAttachment())
            .append("expectFinishTime", getExpectFinishTime())
            .append("actualFinishTime", getActualFinishTime())
            .append("priority", getPriority())
            .append("ticketStatus", getTicketStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
