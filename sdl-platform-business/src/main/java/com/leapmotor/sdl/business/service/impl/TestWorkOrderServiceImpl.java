package com.leapmotor.sdl.business.service.impl;

import java.util.Date;
import java.util.List;
import com.leapmotor.sdl.common.utils.DateUtils;
import com.leapmotor.sdl.common.utils.uuid.IdUtils;
import com.leapmotor.sdl.common.utils.StringUtils;
import com.leapmotor.sdl.system.service.ISysConfigService;
import com.leapmotor.sdl.system.service.ISysUserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.leapmotor.sdl.business.mapper.TestWorkOrderMapper;
import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;
import com.leapmotor.sdl.business.service.IWorkOrderLogService;
import com.leapmotor.sdl.business.enums.WorkOrderStatusEnum;
import com.leapmotor.sdl.business.enums.WorkOrderOperationEnum;

/**
 * 测试工单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class TestWorkOrderServiceImpl implements ITestWorkOrderService 
{
    @Autowired
    private TestWorkOrderMapper testWorkOrderMapper;

    @Autowired
    private IWorkOrderLogService workOrderLogService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 测试工单
     */
    @Override
    public TestWorkOrder selectTestWorkOrderByTicketId(Long ticketId)
    {
        return testWorkOrderMapper.selectTestWorkOrderByTicketId(ticketId);
    }

    /**
     * 查询测试工单列表
     * 
     * @param testWorkOrder 测试工单
     * @return 测试工单
     */
    @Override
    public List<TestWorkOrder> selectTestWorkOrderList(TestWorkOrder testWorkOrder)
    {
        return testWorkOrderMapper.selectTestWorkOrderList(testWorkOrder);
    }

    /**
     * 新增测试工单
     *
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTestWorkOrder(TestWorkOrder testWorkOrder)
    {
        testWorkOrder.setCreateTime(DateUtils.getNowDate());
        testWorkOrder.setTicketId(IdUtils.resourceId());

        // 设置默认状态为未开始
        if (StringUtils.isEmpty(testWorkOrder.getTicketStatus()))
        {
            testWorkOrder.setTicketStatus(WorkOrderStatusEnum.NOT_STARTED.getCode());
        }

        // 设置默认指派人
        if (StringUtils.isEmpty(testWorkOrder.getHandler()))
        {
            String defaultAssignee = configService.selectConfigByKey("workorder.default.assignee");
            if (StringUtils.isNotEmpty(defaultAssignee))
            {
                testWorkOrder.setHandler(defaultAssignee);
                // 根据用户名查找用户ID
                try {
                    var user = userService.selectUserByUserName(defaultAssignee);
                    if (user != null)
                    {
                        testWorkOrder.setHandlerId(user.getUserId());
                    }
                } catch (Exception e) {
                    // 如果查找用户失败，只设置用户名
                }
            }
        }

        int result = testWorkOrderMapper.insertTestWorkOrder(testWorkOrder);

        // 记录创建日志
        if (result > 0)
        {
            workOrderLogService.recordWorkOrderLog(
                testWorkOrder.getTicketId(),
                WorkOrderOperationEnum.CREATE.getCode(),
                null,
                testWorkOrder.getTicketStatus(),
                null,
                testWorkOrder.getHandler(),
                null,
                testWorkOrder.getHandlerId(),
                "创建工单：" + testWorkOrder.getTicketTitle(),
                testWorkOrder.getCreateBy(),
                null // 创建时可能没有操作人ID
            );
        }

        return result;
    }

    /**
     * 修改测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    @Override
    public int updateTestWorkOrder(TestWorkOrder testWorkOrder)
    {
        testWorkOrder.setUpdateTime(DateUtils.getNowDate());
        return testWorkOrderMapper.updateTestWorkOrder(testWorkOrder);
    }

    /**
     * 批量删除测试工单
     * 
     * @param ticketIds 需要删除的测试工单主键
     * @return 结果
     */
    @Override
    public int deleteTestWorkOrderByTicketIds(Long[] ticketIds)
    {
        return testWorkOrderMapper.deleteTestWorkOrderByTicketIds(ticketIds);
    }

    /**
     * 删除测试工单信息
     *
     * @param ticketId 测试工单主键
     * @return 结果
     */
    @Override
    public int deleteTestWorkOrderByTicketId(Long ticketId)
    {
        return testWorkOrderMapper.deleteTestWorkOrderByTicketId(ticketId);
    }

    /**
     * 指派工单
     *
     * @param ticketId 工单ID
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int assignWorkOrder(Long ticketId, Long handlerId, String handlerName, Long operatorId, String operatorName)
    {
        TestWorkOrder workOrder = testWorkOrderMapper.selectTestWorkOrderByTicketId(ticketId);
        if (workOrder == null)
        {
            return 0;
        }

        String oldStatus = workOrder.getTicketStatus();
        String oldHandler = workOrder.getHandler();
        Long oldHandlerId = workOrder.getHandlerId();

        // 检查状态是否可以转换
        if (!WorkOrderStatusEnum.canTransition(oldStatus, WorkOrderStatusEnum.IN_PROGRESS.getCode()))
        {
            throw new RuntimeException("当前状态不允许指派操作");
        }

        // 更新工单状态和处理人
        workOrder.setTicketStatus(WorkOrderStatusEnum.IN_PROGRESS.getCode());
        workOrder.setHandler(handlerName);
        workOrder.setHandlerId(handlerId);
        workOrder.setUpdateTime(DateUtils.getNowDate());

        int result = testWorkOrderMapper.updateTestWorkOrder(workOrder);

        // 记录指派日志
        if (result > 0)
        {
            String operationType = StringUtils.isEmpty(oldHandler) ?
                WorkOrderOperationEnum.ASSIGN.getCode() : WorkOrderOperationEnum.REASSIGN.getCode();
            String operationDesc = StringUtils.isEmpty(oldHandler) ?
                "指派工单给：" + handlerName : "重新指派工单从 " + oldHandler + " 到 " + handlerName;

            workOrderLogService.recordWorkOrderLog(
                ticketId,
                operationType,
                oldStatus,
                WorkOrderStatusEnum.IN_PROGRESS.getCode(),
                oldHandler,
                handlerName,
                oldHandlerId,
                handlerId,
                operationDesc,
                operatorName,
                operatorId
            );
        }

        return result;
    }

    /**
     * 完成工单
     *
     * @param ticketId 工单ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int completeWorkOrder(Long ticketId, Long operatorId, String operatorName)
    {
        TestWorkOrder workOrder = testWorkOrderMapper.selectTestWorkOrderByTicketId(ticketId);
        if (workOrder == null)
        {
            return 0;
        }

        String oldStatus = workOrder.getTicketStatus();

        // 检查状态是否可以转换
        if (!WorkOrderStatusEnum.canTransition(oldStatus, WorkOrderStatusEnum.CLOSED.getCode()))
        {
            throw new RuntimeException("当前状态不允许完成操作");
        }

        // 更新工单状态和完成时间
        workOrder.setTicketStatus(WorkOrderStatusEnum.CLOSED.getCode());
        workOrder.setActualFinishTime(new Date());
        workOrder.setUpdateTime(DateUtils.getNowDate());

        int result = testWorkOrderMapper.updateTestWorkOrder(workOrder);

        // 记录完成日志
        if (result > 0)
        {
            workOrderLogService.recordWorkOrderLog(
                ticketId,
                WorkOrderOperationEnum.COMPLETE.getCode(),
                oldStatus,
                WorkOrderStatusEnum.CLOSED.getCode(),
                workOrder.getHandler(),
                workOrder.getHandler(),
                workOrder.getHandlerId(),
                workOrder.getHandlerId(),
                "完成工单",
                operatorName,
                operatorId
            );
        }

        return result;
    }

    /**
     * 阻塞工单
     *
     * @param ticketId 工单ID
     * @param reason 阻塞原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int blockWorkOrder(Long ticketId, String reason, Long operatorId, String operatorName)
    {
        TestWorkOrder workOrder = testWorkOrderMapper.selectTestWorkOrderByTicketId(ticketId);
        if (workOrder == null)
        {
            return 0;
        }

        String oldStatus = workOrder.getTicketStatus();

        // 任何状态都可以转为被阻塞（除了已关闭）
        if (WorkOrderStatusEnum.CLOSED.getCode().equals(oldStatus))
        {
            throw new RuntimeException("已关闭的工单不能被阻塞");
        }

        // 更新工单状态
        workOrder.setTicketStatus(WorkOrderStatusEnum.BLOCKED.getCode());
        workOrder.setUpdateTime(DateUtils.getNowDate());

        int result = testWorkOrderMapper.updateTestWorkOrder(workOrder);

        // 记录阻塞日志
        if (result > 0)
        {
            workOrderLogService.recordWorkOrderLog(
                ticketId,
                WorkOrderOperationEnum.BLOCK.getCode(),
                oldStatus,
                WorkOrderStatusEnum.BLOCKED.getCode(),
                workOrder.getHandler(),
                workOrder.getHandler(),
                workOrder.getHandlerId(),
                workOrder.getHandlerId(),
                "阻塞工单，原因：" + reason,
                operatorName,
                operatorId
            );
        }

        return result;
    }

    /**
     * 检查并标记延期工单
     *
     * @return 延期工单数量
     */
    @Override
    @Transactional
    public int checkAndMarkOverdueWorkOrders()
    {
        // 查询延期工单
        List<TestWorkOrder> overdueWorkOrders = testWorkOrderMapper.selectOverdueWorkOrders();

        if (overdueWorkOrders.isEmpty())
        {
            return 0;
        }

        // 批量更新状态为延期
        Long[] ticketIds = overdueWorkOrders.stream()
            .map(TestWorkOrder::getTicketId)
            .toArray(Long[]::new);

        int result = testWorkOrderMapper.batchUpdateStatusToOverdue(ticketIds);

        // 记录延期日志
        if (result > 0)
        {
            for (TestWorkOrder workOrder : overdueWorkOrders)
            {
                workOrderLogService.recordWorkOrderLog(
                    workOrder.getTicketId(),
                    WorkOrderOperationEnum.OVERDUE.getCode(),
                    workOrder.getTicketStatus(),
                    WorkOrderStatusEnum.OVERDUE.getCode(),
                    workOrder.getHandler(),
                    workOrder.getHandler(),
                    workOrder.getHandlerId(),
                    workOrder.getHandlerId(),
                    "系统自动标记为延期，期望完成时间：" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, workOrder.getExpectFinishTime()),
                    "系统",
                    null
                );
            }
        }

        return result;
    }

    /**
     * 查询延期工单列表
     *
     * @return 延期工单列表
     */
    @Override
    public List<TestWorkOrder> selectOverdueWorkOrders()
    {
        return testWorkOrderMapper.selectOverdueWorkOrders();
    }
}
