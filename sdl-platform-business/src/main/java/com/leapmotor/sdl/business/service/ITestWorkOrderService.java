package com.leapmotor.sdl.business.service;

import java.util.List;
import com.leapmotor.sdl.business.domain.TestWorkOrder;

/**
 * 测试工单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface ITestWorkOrderService 
{
    /**
     * 查询测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 测试工单
     */
    public TestWorkOrder selectTestWorkOrderByTicketId(Long ticketId);

    /**
     * 查询测试工单列表
     * 
     * @param testWorkOrder 测试工单
     * @return 测试工单集合
     */
    public List<TestWorkOrder> selectTestWorkOrderList(TestWorkOrder testWorkOrder);

    /**
     * 新增测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int insertTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 修改测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int updateTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 批量删除测试工单
     *
     * @param ticketIds 需要删除的测试工单主键集合
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketIds(Long[] ticketIds);

    /**
     * 删除测试工单信息
     *
     * @param ticketId 测试工单主键
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketId(Long ticketId);

    /**
     * 指派工单
     *
     * @param ticketId 工单ID
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int assignWorkOrder(Long ticketId, Long handlerId, String handlerName, Long operatorId, String operatorName);

    /**
     * 完成工单
     *
     * @param ticketId 工单ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int completeWorkOrder(Long ticketId, Long operatorId, String operatorName);

    /**
     * 阻塞工单
     *
     * @param ticketId 工单ID
     * @param reason 阻塞原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int blockWorkOrder(Long ticketId, String reason, Long operatorId, String operatorName);

    /**
     * 检查并标记延期工单
     *
     * @return 延期工单数量
     */
    public int checkAndMarkOverdueWorkOrders();

    /**
     * 查询延期工单列表
     *
     * @return 延期工单列表
     */
    public List<TestWorkOrder> selectOverdueWorkOrders();
}
