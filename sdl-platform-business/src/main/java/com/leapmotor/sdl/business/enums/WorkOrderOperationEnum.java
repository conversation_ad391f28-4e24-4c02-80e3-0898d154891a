package com.leapmotor.sdl.business.enums;

/**
 * 工单操作类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public enum WorkOrderOperationEnum 
{
    /**
     * 创建工单
     */
    CREATE("CREATE", "创建工单"),
    
    /**
     * 指派工单
     */
    ASSIGN("ASSIGN", "指派工单"),
    
    /**
     * 完成工单
     */
    COMPLETE("COMPLETE", "完成工单"),
    
    /**
     * 阻塞工单
     */
    BLOCK("BLOCK", "阻塞工单"),
    
    /**
     * 延期工单
     */
    OVERDUE("OVERDUE", "延期工单"),
    
    /**
     * 状态变更
     */
    STATUS_CHANGE("STATUS_CHANGE", "状态变更"),
    
    /**
     * 重新指派
     */
    REASSIGN("REASSIGN", "重新指派");

    private final String code;
    private final String info;

    WorkOrderOperationEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 根据操作码获取枚举
     * 
     * @param code 操作码
     * @return 枚举
     */
    public static WorkOrderOperationEnum getByCode(String code)
    {
        for (WorkOrderOperationEnum operation : values())
        {
            if (operation.getCode().equals(code))
            {
                return operation;
            }
        }
        return null;
    }
}
