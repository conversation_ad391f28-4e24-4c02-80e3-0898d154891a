<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leapmotor.sdl.business.mapper.WorkOrderLogMapper">
    
    <resultMap type="WorkOrderLog" id="WorkOrderLogResult">
        <result property="logId"    column="log_id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="beforeStatus"    column="before_status"    />
        <result property="afterStatus"    column="after_status"    />
        <result property="beforeHandler"    column="before_handler"    />
        <result property="afterHandler"    column="after_handler"    />
        <result property="beforeHandlerId"    column="before_handler_id"    />
        <result property="afterHandlerId"    column="after_handler_id"    />
        <result property="operationDesc"    column="operation_desc"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectWorkOrderLogVo">
        select log_id, ticket_id, operation_type, before_status, after_status, before_handler, after_handler, before_handler_id, after_handler_id, operation_desc, operator_name, operator_id, operation_time, create_by, create_time, update_by, update_time, remark from sdl_workorder_logs
    </sql>

    <select id="selectWorkOrderLogList" parameterType="WorkOrderLog" resultMap="WorkOrderLogResult">
        <include refid="selectWorkOrderLogVo"/>
        <where>  
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="beforeStatus != null  and beforeStatus != ''"> and before_status = #{beforeStatus}</if>
            <if test="afterStatus != null  and afterStatus != ''"> and after_status = #{afterStatus}</if>
            <if test="beforeHandler != null  and beforeHandler != ''"> and before_handler like concat('%', #{beforeHandler}, '%')</if>
            <if test="afterHandler != null  and afterHandler != ''"> and after_handler like concat('%', #{afterHandler}, '%')</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operationTime != null "> and operation_time = #{operationTime}</if>
        </where>
        order by operation_time desc
    </select>
    
    <select id="selectWorkOrderLogByLogId" parameterType="Long" resultMap="WorkOrderLogResult">
        <include refid="selectWorkOrderLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectWorkOrderLogListByTicketId" parameterType="Long" resultMap="WorkOrderLogResult">
        <include refid="selectWorkOrderLogVo"/>
        where ticket_id = #{ticketId}
        order by operation_time desc
    </select>
        
    <insert id="insertWorkOrderLog" parameterType="WorkOrderLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sdl_workorder_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">ticket_id,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="beforeStatus != null">before_status,</if>
            <if test="afterStatus != null">after_status,</if>
            <if test="beforeHandler != null">before_handler,</if>
            <if test="afterHandler != null">after_handler,</if>
            <if test="beforeHandlerId != null">before_handler_id,</if>
            <if test="afterHandlerId != null">after_handler_id,</if>
            <if test="operationDesc != null">operation_desc,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketId != null">#{ticketId},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="beforeStatus != null">#{beforeStatus},</if>
            <if test="afterStatus != null">#{afterStatus},</if>
            <if test="beforeHandler != null">#{beforeHandler},</if>
            <if test="afterHandler != null">#{afterHandler},</if>
            <if test="beforeHandlerId != null">#{beforeHandlerId},</if>
            <if test="afterHandlerId != null">#{afterHandlerId},</if>
            <if test="operationDesc != null">#{operationDesc},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateWorkOrderLog" parameterType="WorkOrderLog">
        update sdl_workorder_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="beforeStatus != null">before_status = #{beforeStatus},</if>
            <if test="afterStatus != null">after_status = #{afterStatus},</if>
            <if test="beforeHandler != null">before_handler = #{beforeHandler},</if>
            <if test="afterHandler != null">after_handler = #{afterHandler},</if>
            <if test="beforeHandlerId != null">before_handler_id = #{beforeHandlerId},</if>
            <if test="afterHandlerId != null">after_handler_id = #{afterHandlerId},</if>
            <if test="operationDesc != null">operation_desc = #{operationDesc},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteWorkOrderLogByLogId" parameterType="Long">
        delete from sdl_workorder_logs where log_id = #{logId}
    </delete>

    <delete id="deleteWorkOrderLogByLogIds" parameterType="String">
        delete from sdl_workorder_logs where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
