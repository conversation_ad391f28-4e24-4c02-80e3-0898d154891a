#!/bin/bash

# 工单生命周期管理系统测试脚本
# 
# 作者: panxiaobo
# 日期: 2025-07-20

echo "=========================================="
echo "开始运行工单生命周期管理系统测试"
echo "=========================================="

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
cd "$PROJECT_ROOT"

echo "项目根目录: $PROJECT_ROOT"

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven 未安装或不在PATH中"
    exit 1
fi

echo "Maven版本:"
mvn --version

echo ""
echo "=========================================="
echo "运行单元测试"
echo "=========================================="

# 运行业务模块的测试
echo "运行业务模块测试..."
cd sdl-platform-business

# 运行工单服务测试
echo "1. 运行工单服务测试..."
mvn test -Dtest=TestWorkOrderServiceTest

if [ $? -ne 0 ]; then
    echo "工单服务测试失败"
    exit 1
fi

# 运行工单状态枚举测试
echo "2. 运行工单状态枚举测试..."
mvn test -Dtest=WorkOrderStatusEnumTest

if [ $? -ne 0 ]; then
    echo "工单状态枚举测试失败"
    exit 1
fi

# 运行工单控制器测试
echo "3. 运行工单控制器测试..."
mvn test -Dtest=TestWorkOrderControllerTest

if [ $? -ne 0 ]; then
    echo "工单控制器测试失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "运行集成测试"
echo "=========================================="

# 返回项目根目录
cd "$PROJECT_ROOT"

# 运行完整的测试套件
echo "运行完整测试套件..."
mvn clean test -pl sdl-platform-business

if [ $? -ne 0 ]; then
    echo "集成测试失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "生成测试报告"
echo "=========================================="

# 生成测试报告
mvn surefire-report:report -pl sdl-platform-business

echo "测试报告生成完成，位置: sdl-platform-business/target/site/surefire-report.html"

echo ""
echo "=========================================="
echo "测试完成"
echo "=========================================="

echo "✅ 所有测试通过！"
echo ""
echo "测试覆盖的功能："
echo "- 工单创建和默认状态设置"
echo "- 工单指派功能"
echo "- 工单完成功能"
echo "- 工单阻塞功能"
echo "- 工单延期检查"
echo "- 工单状态流转验证"
echo "- 工单日志记录"
echo "- API接口测试"
echo ""
echo "下一步建议："
echo "1. 部署到测试环境进行手动测试"
echo "2. 执行数据库脚本创建相关表和配置"
echo "3. 配置定时任务"
echo "4. 测试前端界面功能"

exit 0
